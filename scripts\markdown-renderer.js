/**
 * Markdown Renderer Module
 * Handles markdown parsing and rendering with syntax highlighting
 */

class MarkdownRenderer {
  constructor() {
    this.initializeMarked();
    this.initializeHighlightJs();
  }

  /**
   * Initialize marked.js with custom configuration
   */
  initializeMarked() {
    if (typeof marked === 'undefined') {
      console.error('Marked.js is not loaded');
      return;
    }

    // Configure marked options
    marked.setOptions({
      highlight: (code, lang) => this.highlightCode(code, lang),
      breaks: true,
      gfm: true,
      tables: true,
      sanitize: false,
      smartLists: true,
      smartypants: true,
      xhtml: false
    });

    // Custom renderer for better control
    const renderer = new marked.Renderer();
    
    // Custom heading renderer with anchor links
    renderer.heading = (text, level) => {
      const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-');
      return `
        <h${level} id="${escapedText}">
          <a href="#${escapedText}" class="header-link" aria-hidden="true">#</a>
          ${text}
        </h${level}>
      `;
    };

    // Custom code block renderer
    renderer.code = (code, language) => {
      const validLanguage = language && hljs.getLanguage(language) ? language : 'plaintext';
      const highlightedCode = this.highlightCode(code, validLanguage);
      
      return `
        <div class="code-block">
          <div class="code-header">
            <span class="language-label">${validLanguage}</span>
            <button class="copy-code-btn" onclick="this.copyCode(this)" title="Copy code">
              <i class="mdi mdi-content-copy"></i>
            </button>
          </div>
          <pre><code class="hljs language-${validLanguage}">${highlightedCode}</code></pre>
        </div>
      `;
    };

    // Custom table renderer with better styling
    renderer.table = (header, body) => {
      return `
        <div class="table-wrapper">
          <table class="markdown-table">
            <thead>${header}</thead>
            <tbody>${body}</tbody>
          </table>
        </div>
      `;
    };

    // Custom blockquote renderer
    renderer.blockquote = (quote) => {
      return `<blockquote class="markdown-blockquote">${quote}</blockquote>`;
    };

    // Custom link renderer with external link handling
    renderer.link = (href, title, text) => {
      const isExternal = href.startsWith('http') && !href.includes(window.location.hostname);
      const titleAttr = title ? ` title="${title}"` : '';
      const targetAttr = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
      const externalIcon = isExternal ? ' <i class="mdi mdi-open-in-new" aria-hidden="true"></i>' : '';
      
      return `<a href="${href}"${titleAttr}${targetAttr}>${text}${externalIcon}</a>`;
    };

    // Custom image renderer with lazy loading
    renderer.image = (href, title, text) => {
      const titleAttr = title ? ` title="${title}"` : '';
      return `
        <div class="image-wrapper">
          <img src="${href}" alt="${text}"${titleAttr} loading="lazy" />
          ${title ? `<div class="image-caption">${title}</div>` : ''}
        </div>
      `;
    };

    marked.use({ renderer });
  }

  /**
   * Initialize highlight.js
   */
  initializeHighlightJs() {
    if (typeof hljs === 'undefined') {
      console.error('Highlight.js is not loaded');
      return;
    }

    // Configure highlight.js
    hljs.configure({
      tabReplace: '  ',
      useBR: false,
      classPrefix: 'hljs-'
    });
  }

  /**
   * Highlight code with syntax highlighting
   * @param {string} code - The code to highlight
   * @param {string} language - The programming language
   * @returns {string} Highlighted code HTML
   */
  highlightCode(code, language) {
    if (!code) return '';

    try {
      if (language && hljs.getLanguage(language)) {
        return hljs.highlight(code, { language }).value;
      } else {
        return hljs.highlightAuto(code).value;
      }
    } catch (error) {
      console.warn('Syntax highlighting failed:', error);
      return EditorUtils.escapeHtml(code);
    }
  }

  /**
   * Render markdown to HTML
   * @param {string} markdown - The markdown content
   * @returns {string} Rendered HTML
   */
  render(markdown) {
    if (!markdown || typeof markdown !== 'string') {
      return '';
    }

    try {
      return marked.parse(markdown);
    } catch (error) {
      console.error('Markdown rendering failed:', error);
      return `<div class="error">Error rendering markdown: ${error.message}</div>`;
    }
  }

  /**
   * Render markdown with custom processing
   * @param {string} markdown - The markdown content
   * @returns {string} Processed and rendered HTML
   */
  renderWithProcessing(markdown) {
    if (!markdown) return '';

    // Pre-process markdown for custom features
    let processedMarkdown = this.preprocessMarkdown(markdown);
    
    // Render to HTML
    let html = this.render(processedMarkdown);
    
    // Post-process HTML
    html = this.postprocessHtml(html);
    
    return html;
  }

  /**
   * Pre-process markdown for custom features
   * @param {string} markdown - The markdown content
   * @returns {string} Processed markdown
   */
  preprocessMarkdown(markdown) {
    // Add support for task lists if not already supported
    markdown = markdown.replace(/^(\s*)- \[([ x])\] (.+)$/gm, (match, indent, checked, text) => {
      const isChecked = checked.toLowerCase() === 'x';
      return `${indent}- <input type="checkbox" ${isChecked ? 'checked' : ''} disabled> ${text}`;
    });

    // Add support for keyboard shortcuts
    markdown = markdown.replace(/\[\[([^\]]+)\]\]/g, '<kbd>$1</kbd>');

    // Add support for highlights
    markdown = markdown.replace(/==(.*?)==/g, '<mark>$1</mark>');

    return markdown;
  }

  /**
   * Post-process HTML for additional features
   * @param {string} html - The rendered HTML
   * @returns {string} Processed HTML
   */
  postprocessHtml(html) {
    // Add copy functionality to code blocks
    html = html.replace(/<button class="copy-code-btn"[^>]*>/g, (match) => {
      return match.replace('onclick="this.copyCode(this)"', 'onclick="MarkdownRenderer.copyCode(this)"');
    });

    return html;
  }

  /**
   * Copy code to clipboard
   * @param {HTMLElement} button - The copy button element
   */
  static copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const code = codeBlock.querySelector('code').textContent;
    
    navigator.clipboard.writeText(code).then(() => {
      const originalIcon = button.innerHTML;
      button.innerHTML = '<i class="mdi mdi-check"></i>';
      button.title = 'Copied!';
      
      setTimeout(() => {
        button.innerHTML = originalIcon;
        button.title = 'Copy code';
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

  /**
   * Get table of contents from markdown
   * @param {string} markdown - The markdown content
   * @returns {Array} Array of heading objects
   */
  getTableOfContents(markdown) {
    const headings = [];
    const lines = markdown.split('\n');
    
    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = match[1].length;
        const text = match[2].trim();
        const id = text.toLowerCase().replace(/[^\w]+/g, '-');
        
        headings.push({
          level,
          text,
          id,
          line: index + 1
        });
      }
    });
    
    return headings;
  }

  /**
   * Extract metadata from markdown (front matter)
   * @param {string} markdown - The markdown content
   * @returns {Object} Metadata object and content
   */
  extractMetadata(markdown) {
    const frontMatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
    const match = markdown.match(frontMatterRegex);
    
    if (!match) {
      return { metadata: {}, content: markdown };
    }
    
    const yamlContent = match[1];
    const content = match[2];
    const metadata = {};
    
    // Simple YAML parser for basic key-value pairs
    yamlContent.split('\n').forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();
        metadata[key] = value.replace(/^["']|["']$/g, ''); // Remove quotes
      }
    });
    
    return { metadata, content };
  }
}

// Create global instance
window.markdownRenderer = new MarkdownRenderer();

// Export for module use
window.MarkdownRenderer = MarkdownRenderer;
