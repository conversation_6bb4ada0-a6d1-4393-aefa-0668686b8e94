/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  
  /* Background Colors */
  --bg-color: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-hover: #f5f5f5;
  --bg-active: #e9ecef;
  
  /* Text Colors */
  --text-color: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  
  /* Border Colors */
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  --border-dark: #adb5bd;
  
  /* Layout */
  --sidebar-width: 200px;
  --header-height: 60px;
  --status-bar-height: 28px;
  --border-radius: 6px;
  --border-radius-sm: 4px;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Typography */
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* ===== DARK MODE VARIABLES ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #ecf0f1;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    
    --bg-color: #1e1e1e;
    --bg-secondary: #252525;
    --bg-hover: #2a2a2a;
    --bg-active: #333333;
    
    --text-color: #f5f5f5;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;
  }
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background: var(--bg-color);
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

button {
  font-family: inherit;
  font-size: inherit;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
}

button:focus-visible {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* ===== HEADER STYLES ===== */
.app-header {
  height: var(--header-height);
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 100;
}

.logo {
  font-weight: 600;
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-right: var(--spacing-lg);
}

.logo i {
  font-size: 1.5em;
  color: var(--secondary-color);
}

/* ===== TOOLBAR STYLES ===== */
.toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-grow: 1;
  overflow-x: auto;
  padding: var(--spacing-xs) 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.toolbar button {
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.toolbar button:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.toolbar button:active {
  background: var(--bg-active);
}

.toolbar button i {
  font-size: 1.1em;
}

.toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background: var(--border-light);
  margin: 0 var(--spacing-xs);
}

/* ===== DROPDOWN STYLES ===== */
.dropdown {
  position: relative;
}

.dropdown-trigger {
  position: relative;
}

.dropdown-content {
  display: none;
  position: fixed;
  background: var(--bg-color);
  min-width: 220px;
  max-width: 300px;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 2000;
  padding: var(--spacing-xs) 0;
}

/* Dropdown positioning will be handled by JavaScript */

.dropdown:hover .dropdown-content,
.dropdown:focus-within .dropdown-content,
.dropdown.open .dropdown-content {
  display: block;
}

.dropdown-content button {
  width: 100%;
  text-align: left;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0;
  justify-content: space-between;
}

.dropdown-content button:hover {
  background: var(--bg-hover);
}

.dropdown-content hr {
  border: none;
  border-top: 1px solid var(--border-light);
  margin: var(--spacing-xs) 0;
}

.dropdown-content kbd {
  font-size: 0.75em;
  color: var(--text-muted);
  background: var(--bg-secondary);
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: var(--font-family-mono);
}

/* ===== VIEW MODE TOGGLE ===== */
.view-mode-toggle {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-left: auto;
}

.view-mode-toggle button {
  border-radius: 0;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-color);
  border-right: 1px solid var(--border-color);
  font-size: var(--font-size-sm);
}

.view-mode-toggle button:last-child {
  border-right: none;
}

.view-mode-toggle button.active {
  background: var(--secondary-color);
  color: white;
}

.view-mode-toggle button:not(.active):hover {
  background: var(--bg-hover);
}

/* ===== MAIN CONTAINER ===== */
.main-container {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
}

.editor-container {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
}

/* ===== EDITOR STYLES ===== */
.editor-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.editor {
  flex-grow: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
  line-height: 1.8;
  font-size: var(--font-size-base);
  font-family: var(--font-family-mono);
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: var(--bg-color);
  color: var(--text-color);
  resize: none;
  border: none;
}

.editor:focus {
  background: var(--bg-color);
}

/* ===== PREVIEW STYLES ===== */
.preview {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
  border-left: 1px solid var(--border-color);
  background: var(--bg-color);
  font-family: var(--font-family);
  line-height: 1.7;
  cursor: text;
  transition: all var(--transition-fast);
  position: relative;
}

.preview:hover {
  background: var(--bg-hover);
}

.preview:hover::after {
  content: "Double-click to edit";
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: var(--text-muted);
  color: var(--bg-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  opacity: 0.8;
  pointer-events: none;
  z-index: 10;
}

.preview.preview-editing {
  background: var(--bg-secondary);
  border-left-color: var(--secondary-color);
  border-left-width: 3px;
  outline: none;
}

.preview.preview-editing:focus {
  background: var(--bg-color);
}

/* ===== STATUS BAR ===== */
.status-bar {
  height: var(--status-bar-height);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.file-name {
  font-weight: 500;
}

.directory-info {
  color: var(--secondary-color);
  font-size: var(--font-size-sm);
  margin-left: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-hover);
  border-radius: var(--border-radius-sm);
  cursor: help;
}

.statistics {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.statistics .separator {
  color: var(--text-muted);
}

/* ===== CODE BLOCK ENHANCEMENTS ===== */
.code-block {
  position: relative;
  margin: 1em 0;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-bottom: none;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.language-label {
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75em;
  letter-spacing: 0.5px;
}

.copy-code-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.8em;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.copy-code-btn:hover {
  background: var(--bg-hover);
  color: var(--text-color);
}

.copy-code-btn i {
  font-size: 1em;
}

/* ===== TABLE WRAPPER ===== */
.table-wrapper {
  overflow-x: auto;
  margin: 1em 0;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.markdown-table {
  margin: 0;
  border: none;
  border-radius: 0;
}

/* ===== IMAGE WRAPPER ===== */
.image-wrapper {
  text-align: center;
  margin: 1.5em 0;
}

.image-caption {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-sm);
  font-style: italic;
}

/* ===== ENHANCED BLOCKQUOTE ===== */
.markdown-blockquote {
  position: relative;
  margin: 1.5em 0;
  padding: 1em 1em 1em 2em;
}

.markdown-blockquote::before {
  content: '"';
  position: absolute;
  left: 0.5em;
  top: 0.2em;
  font-size: 2em;
  color: var(--secondary-color);
  font-weight: bold;
  line-height: 1;
}

/* ===== HEADER LINKS ===== */
.header-link {
  opacity: 0;
  margin-left: 0.5em;
  color: var(--text-muted);
  text-decoration: none;
  font-weight: normal;
  transition: opacity var(--transition-fast);
}

.preview h1:hover .header-link,
.preview h2:hover .header-link,
.preview h3:hover .header-link,
.preview h4:hover .header-link,
.preview h5:hover .header-link,
.preview h6:hover .header-link {
  opacity: 1;
}

/* ===== TASK LISTS ===== */
.preview input[type="checkbox"] {
  margin-right: var(--spacing-sm);
  transform: scale(1.1);
}

/* ===== KEYBOARD SHORTCUTS ===== */
kbd {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: 0.125rem 0.25rem;
  font-family: var(--font-family-mono);
  font-size: 0.8em;
  color: var(--text-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== HIGHLIGHTS ===== */
mark {
  background: #fff3cd;
  color: #856404;
  padding: 0.1em 0.2em;
  border-radius: var(--border-radius-sm);
}

@media (prefers-color-scheme: dark) {
  mark {
    background: #664d03;
    color: #fff3cd;
  }
}

/* ===== NOTIFICATIONS ===== */
.error-notification,
.success-notification {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== FOCUS STYLES ===== */
.editor.focused {
  box-shadow: inset 0 0 0 2px var(--secondary-color);
}

/* ===== OFFLINE INDICATOR ===== */
.status-bar.offline::after {
  content: " (Offline)";
  color: var(--accent-color);
  font-weight: 500;
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== PREVIEW CONTENT STYLES ===== */
.preview h1,
.preview h2,
.preview h3,
.preview h4,
.preview h5,
.preview h6 {
  margin-top: 1.5em;
  margin-bottom: 1em;
  line-height: 1.3;
  font-weight: 600;
}

.preview h1 {
  font-size: 2em;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.3em;
}

.preview h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 0.3em;
}

.preview h3 {
  font-size: 1.25em;
}

.preview h4 {
  font-size: 1.1em;
}

.preview h5,
.preview h6 {
  font-size: 1em;
}

.preview p {
  margin: 1em 0;
}

.preview ul,
.preview ol {
  padding-left: 2em;
  margin: 1em 0;
}

.preview li {
  margin: 0.25em 0;
}

.preview blockquote {
  border-left: 4px solid var(--secondary-color);
  margin: 1em 0;
  padding: 0.5em 0 0.5em 1em;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.preview pre {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  overflow-x: auto;
  border: 1px solid var(--border-light);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  line-height: 1.4;
}

.preview code {
  font-family: var(--font-family-mono);
  background: var(--bg-secondary);
  padding: 0.2em 0.4em;
  border-radius: var(--border-radius-sm);
  font-size: 0.9em;
  border: 1px solid var(--border-light);
}

.preview pre code {
  background: transparent;
  padding: 0;
  border: none;
  border-radius: 0;
}

.preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.preview th,
.preview td {
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
}

.preview th {
  background: var(--bg-secondary);
  font-weight: 600;
}

.preview tr:nth-child(even) {
  background: var(--bg-secondary);
}

.preview img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.preview a {
  color: var(--secondary-color);
  text-decoration: none;
}

.preview a:hover {
  text-decoration: underline;
}

.preview hr {
  border: none;
  border-top: 2px solid var(--border-color);
  margin: 2em 0;
}

/* ===== DARK MODE PREVIEW ADJUSTMENTS ===== */
@media (prefers-color-scheme: dark) {
  .preview pre,
  .preview code {
    background: #282c34;
    border-color: var(--border-color);
  }

  .preview blockquote {
    background: var(--bg-secondary);
  }
}

/* ===== HIDDEN FILE INPUTS ===== */
#file-input,
#directory-file-input {
  display: none !important;
  position: absolute;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .toolbar {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .toolbar::-webkit-scrollbar {
    display: none;
  }

  .toolbar button span {
    display: none;
  }

  .view-mode-toggle button span {
    display: none;
  }

  .editor,
  .preview {
    padding: var(--spacing-md);
  }

  .modal-content {
    width: 95%;
    margin: var(--spacing-md);
  }

  .modal-header,
  .modal-body {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .logo span {
    display: none;
  }

  .statistics {
    font-size: 0.75rem;
  }
}

/* ===== MODAL STYLES ===== */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: var(--font-size-lg);
}

.modal-close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-body p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
}

/* ===== DIRECTORY MODAL STYLES ===== */
.directory-actions {
  margin-bottom: var(--spacing-lg);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-color);
  color: var(--text-color);
  text-decoration: none;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn:hover {
  background: var(--bg-hover);
  border-color: var(--border-dark);
}

.btn-primary {
  background: var(--secondary-color);
  color: white;
  border-color: var(--secondary-color);
}

.btn-primary:hover {
  background: #2980b9;
  border-color: #2980b9;
}

.recent-list h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-color);
  font-size: var(--font-size-base);
}

.recent-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recent-list li {
  margin-bottom: var(--spacing-xs);
}

.recent-directory-item,
.file-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  background: var(--bg-color);
  color: var(--text-color);
  text-align: left;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.recent-directory-item:hover,
.file-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-color);
}

.recent-list .empty {
  color: var(--text-muted);
  font-style: italic;
  text-align: center;
  padding: var(--spacing-md);
}

/* ===== FILE SELECTION MODAL ===== */
.file-selection-modal .modal-content {
  max-width: 600px;
}

.file-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}

.file-list li {
  margin-bottom: var(--spacing-xs);
}

.file-item i {
  color: var(--secondary-color);
}

/* ===== PREVIEW EDITING INDICATOR ===== */
#preview-edit-indicator {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--secondary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1500;
  display: none;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  animation: slideInRight 0.3s ease-out;
}

#preview-edit-indicator i {
  font-size: 1.1em;
}

/* ===== MODAL ANIMATIONS ===== */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
