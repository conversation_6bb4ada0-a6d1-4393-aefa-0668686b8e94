# Testing New Features

## 1. Dropdown Menu Test
- Click on the "File" menu to test the improved dropdown positioning
- The menu should appear properly within the viewport

## 2. Preview Editing Test
- Double-click anywhere in this preview to start editing
- You should see an editing indicator appear
- Make changes directly in the preview
- Press Escape or click outside to finish editing

### Sample Content for Testing

**Bold text** and *italic text*

- List item 1
- List item 2
- List item 3

> This is a blockquote for testing

```javascript
// Code block for testing
function test() {
    console.log("Testing preview editing");
}
```

| Column 1 | Column 2 |
|----------|----------|
| Data 1   | Data 2   |
| Data 3   | Data 4   |

---

Try editing this content directly in the preview!
