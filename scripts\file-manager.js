/**
 * File Manager Module
 * Handles file operations like open, save, export
 */

class FileManager {
  constructor() {
    this.fileInput = null;
    this.currentFile = null;
    this.recentFiles = [];
    this.maxRecentFiles = 10;
    
    this.initialize();
  }

  /**
   * Initialize file manager
   */
  initialize() {
    this.fileInput = document.getElementById('file-input');
    if (!this.fileInput) {
      console.error('File input element not found');
      return;
    }

    this.setupEventListeners();
    this.loadRecentFiles();
    this.setupKeyboardShortcuts();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // File input change event
    this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    
    // Drag and drop events
    document.addEventListener('dragover', this.handleDragOver.bind(this));
    document.addEventListener('drop', this.handleDrop.bind(this));
    
    // Before unload warning for unsaved changes
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    EditorUtils.addKeyboardShortcut('ctrl+n', () => this.newFile());
    EditorUtils.addKeyboardShortcut('ctrl+o', () => this.openFile());
    EditorUtils.addKeyboardShortcut('ctrl+s', () => this.saveFile());
    EditorUtils.addKeyboardShortcut('ctrl+shift+s', () => this.saveAsFile());
    EditorUtils.addKeyboardShortcut('ctrl+shift+e', () => this.exportHtml());
  }

  /**
   * Create new file
   */
  newFile() {
    if (window.editor.isModified) {
      const shouldSave = EditorUtils.showConfirmDialog(
        'You have unsaved changes. Do you want to save before creating a new file?'
      );
      
      if (shouldSave) {
        this.saveFile();
      }
    }

    window.editor.setContent('');
    window.editor.setFileName('Untitled.md');
    window.editor.markAsModified(false);
    this.currentFile = null;
    
    // Focus editor
    window.editor.focus();
  }

  /**
   * Open file dialog
   */
  openFile() {
    if (window.editor.isModified) {
      const shouldSave = EditorUtils.showConfirmDialog(
        'You have unsaved changes. Do you want to save before opening a new file?'
      );
      
      if (shouldSave) {
        this.saveFile();
      }
    }

    this.fileInput.click();
  }

  /**
   * Handle file selection
   */
  async handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      await this.loadFile(file);
    }
    
    // Reset file input
    this.fileInput.value = '';
  }

  /**
   * Load file content
   */
  async loadFile(file) {
    try {
      const content = await EditorUtils.readFileAsText(file);
      
      window.editor.setContent(content);
      window.editor.setFileName(file.name);
      window.editor.markAsModified(false);
      
      this.currentFile = {
        name: file.name,
        content: content,
        lastModified: file.lastModified
      };
      
      this.addToRecentFiles(file.name);
      
      // Focus editor
      window.editor.focus();
      
    } catch (error) {
      console.error('Error loading file:', error);
      alert('Error loading file: ' + error.message);
    }
  }

  /**
   * Save current file
   */
  saveFile() {
    const content = window.editor.getContent();
    const filename = window.editor.currentFileName;
    
    EditorUtils.downloadFile(content, filename, 'text/markdown');
    
    window.editor.markAsModified(false);
    
    this.currentFile = {
      name: filename,
      content: content,
      lastModified: Date.now()
    };
    
    this.addToRecentFiles(filename);
  }

  /**
   * Save file with new name
   */
  saveAsFile() {
    const currentName = window.editor.currentFileName;
    const newName = EditorUtils.showPromptDialog('Enter filename:', currentName);
    
    if (newName) {
      window.editor.setFileName(newName);
      this.saveFile();
    }
  }

  /**
   * Export to HTML
   */
  exportHtml() {
    const content = window.editor.getContent();
    const { metadata, content: markdownContent } = window.markdownRenderer.extractMetadata(content);
    const html = window.markdownRenderer.renderWithProcessing(markdownContent);
    
    const title = metadata.title || window.editor.currentFileName.replace(/\.md$/, '');
    const author = metadata.author || '';
    const description = metadata.description || '';
    
    const fullHtml = this.generateHtmlDocument(html, title, author, description);
    const filename = window.editor.currentFileName.replace(/\.md$/, '.html');
    
    EditorUtils.downloadFile(fullHtml, filename, 'text/html');
  }

  /**
   * Generate complete HTML document
   */
  generateHtmlDocument(content, title, author, description) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${EditorUtils.escapeHtml(title)}</title>
  ${author ? `<meta name="author" content="${EditorUtils.escapeHtml(author)}">` : ''}
  ${description ? `<meta name="description" content="${EditorUtils.escapeHtml(description)}">` : ''}
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background: #fff;
    }
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.5em;
      margin-bottom: 1em;
      line-height: 1.3;
      font-weight: 600;
    }
    
    h1 { font-size: 2em; border-bottom: 2px solid #eee; padding-bottom: 0.3em; }
    h2 { font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }
    h3 { font-size: 1.25em; }
    
    p { margin: 1em 0; }
    
    ul, ol { padding-left: 2em; margin: 1em 0; }
    li { margin: 0.25em 0; }
    
    blockquote {
      border-left: 4px solid #3498db;
      margin: 1em 0;
      padding: 0.5em 0 0.5em 1em;
      color: #666;
      background: #f8f9fa;
      border-radius: 0 4px 4px 0;
    }
    
    pre {
      background: #f6f8fa;
      padding: 1rem;
      border-radius: 6px;
      overflow-x: auto;
      border: 1px solid #e1e4e8;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }
    
    code {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      background: #f6f8fa;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 0.9em;
      border: 1px solid #e1e4e8;
    }
    
    pre code {
      background: transparent;
      padding: 0;
      border: none;
      border-radius: 0;
    }
    
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 1em 0;
      border: 1px solid #ddd;
      border-radius: 6px;
      overflow: hidden;
    }
    
    th, td {
      border: 1px solid #ddd;
      padding: 0.5rem 1rem;
      text-align: left;
    }
    
    th {
      background: #f8f9fa;
      font-weight: 600;
    }
    
    tr:nth-child(even) {
      background: #f8f9fa;
    }
    
    img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    a {
      color: #3498db;
      text-decoration: none;
    }
    
    a:hover {
      text-decoration: underline;
    }
    
    hr {
      border: none;
      border-top: 2px solid #eee;
      margin: 2em 0;
    }
    
    .header-link {
      opacity: 0;
      margin-left: 0.5em;
      font-weight: normal;
    }
    
    h1:hover .header-link,
    h2:hover .header-link,
    h3:hover .header-link,
    h4:hover .header-link,
    h5:hover .header-link,
    h6:hover .header-link {
      opacity: 1;
    }
    
    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }
    }
    
    @media print {
      body {
        max-width: none;
        margin: 0;
        padding: 1rem;
      }
      
      .header-link {
        display: none;
      }
    }
  </style>
</head>
<body>
  ${content}
</body>
</html>`;
  }

  /**
   * Export to PDF (using browser print)
   */
  exportPdf() {
    const content = window.editor.getContent();
    const html = window.markdownRenderer.renderWithProcessing(content);
    
    // Create a new window with the content
    const printWindow = window.open('', '_blank');
    const title = window.editor.currentFileName.replace(/\.md$/, '');
    const fullHtml = this.generateHtmlDocument(html, title);
    
    printWindow.document.write(fullHtml);
    printWindow.document.close();
    
    // Wait for content to load, then print
    printWindow.onload = () => {
      printWindow.print();
    };
  }

  /**
   * Handle drag over
   */
  handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'copy';
  }

  /**
   * Handle file drop
   */
  async handleDrop(event) {
    event.preventDefault();
    
    const files = Array.from(event.dataTransfer.files);
    const markdownFiles = files.filter(file => 
      file.type === 'text/markdown' || 
      file.name.endsWith('.md') || 
      file.name.endsWith('.txt')
    );
    
    if (markdownFiles.length > 0) {
      await this.loadFile(markdownFiles[0]);
    }
  }

  /**
   * Handle before unload
   */
  handleBeforeUnload(event) {
    if (window.editor.isModified) {
      const message = 'You have unsaved changes. Are you sure you want to leave?';
      event.returnValue = message;
      return message;
    }
  }

  /**
   * Add file to recent files list
   */
  addToRecentFiles(filename) {
    // Remove if already exists
    this.recentFiles = this.recentFiles.filter(file => file !== filename);
    
    // Add to beginning
    this.recentFiles.unshift(filename);
    
    // Limit to max recent files
    if (this.recentFiles.length > this.maxRecentFiles) {
      this.recentFiles = this.recentFiles.slice(0, this.maxRecentFiles);
    }
    
    this.saveRecentFiles();
  }

  /**
   * Load recent files from localStorage
   */
  loadRecentFiles() {
    try {
      const stored = localStorage.getItem('markdownEditor.recentFiles');
      if (stored) {
        this.recentFiles = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load recent files:', error);
      this.recentFiles = [];
    }
  }

  /**
   * Save recent files to localStorage
   */
  saveRecentFiles() {
    try {
      localStorage.setItem('markdownEditor.recentFiles', JSON.stringify(this.recentFiles));
    } catch (error) {
      console.warn('Failed to save recent files:', error);
    }
  }

  /**
   * Get recent files
   */
  getRecentFiles() {
    return [...this.recentFiles];
  }

  /**
   * Clear recent files
   */
  clearRecentFiles() {
    this.recentFiles = [];
    this.saveRecentFiles();
  }

  /**
   * Get current file info
   */
  getCurrentFileInfo() {
    return this.currentFile ? { ...this.currentFile } : null;
  }

  /**
   * Check if file has been modified externally
   */
  checkFileModification() {
    // This would be implemented for desktop applications
    // For web applications, this is not applicable
    return false;
  }
}

// Create global instance
window.fileManager = new FileManager();
